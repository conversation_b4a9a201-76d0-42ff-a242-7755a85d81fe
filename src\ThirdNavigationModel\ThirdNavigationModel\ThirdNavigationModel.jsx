import React from "react";
import "./style.css";

export default function ThirdNavigationModel({ onClose })  {
  return (
    <div className="modals">
      <div className="frame">
        <div className="text-wrapper">Confirm?</div>

        <div className="asset">
          <div className="layer">
            <div className="div">times</div>
          </div>
        </div>
      </div>

      <div className="duis-leo-sed-wrapper">
        <p className="duis-leo-sed">
          <span className="span">
            Duis leo. Sed fringilla mauris sit amet nibh. Donec sodales
            sagittis:
            <br />
            <br />
          </span>

          <span className="text-wrapper-2">Maecenas tempus: </span>

          <span className="span">0030012</span>

          <span className="text-wrapper-2">
            &nbsp;&nbsp; <br />
            <br />
          </span>

          <span className="span">
            Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aenean
            commodo ligula eget dolor. Aenean massa. Cum sociis natoque
            penatibus et magnis dis parturient montes, nascetur ridiculus
          </span>
        </p>
      </div>

      <div className="frame-2">
        <div className="component" onClick={onClose}>
          <div className="div-wrapper">
            <div className="PRIMARY">YES</div>
          </div>
        </div>

        <button className="button" onClick={onClose}>
          <div className="div-wrapper">
            <div className="SECONDARY">NO</div>
          </div>
        </button>
      </div>
    </div>
  );
};
